import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import {
  UserProfile,
  ProfileUpdateRequest,
  SkillEndorsementRequest,
  ProfileViewRequest,
  ProfileAnalytics,
  ProfileSearchFilters,
  ProfileSearchResult,
  BlogPost,
  Achievement,
  Certification,
  WorkExperience,
  PortfolioItem,
  ProfileSkill
} from '../models/profile.models';
import {
  ApiResponse,
  ProfileSearchRequest,
  CreateProfileRequest,
  UpdateProfileRequest
} from '../../shared/models/api-response.model';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  private readonly API_URL = `${environment.apiUrl}/profile`;

  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  public currentProfile$ = this.currentProfileSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get HTTP headers with authentication token
   */
  private getHttpHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    let headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  // Profile CRUD Operations
  getProfile(identifier: string): Observable<UserProfile> {
    // Check if identifier is a slug or ID
    const url = isNaN(Number(identifier))
      ? `${this.API_URL}/slug/${identifier}`
      : `${this.API_URL}/${identifier}`;

    return this.http.get<ApiResponse<UserProfile>>(url)
      .pipe(
        map(response => response.data!),
        tap(profile => {
          if (profile) {
            this.currentProfileSubject.next(profile);
            // Record profile view
            this.recordProfileView({
              profileId: profile.id,
              referrer: document.referrer,
              userAgent: navigator.userAgent
            }).subscribe();
          }
        }),
        catchError(this.handleError)
      );
  }

  getCurrentUserProfile(): Observable<UserProfile> {
    return this.http.get<ApiResponse<UserProfile>>(`${this.API_URL}/me`, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(response => response.data!),
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {
    const updateRequest: UpdateProfileRequest = {
      firstName: updates.firstName,
      lastName: updates.lastName,
      professionalTitle: updates.professionalTitle,
      headline: updates.headline,
      summary: updates.summary,
      profilePhoto:
      location: updates.location ? {
        city: updates.location.city,
        state: updates.location.state,
        country: updates.location.country,
        displayLocation: updates.location.displayLocation
      } : undefined,
      contactInfo: updates.contactInfo ? {
        email: updates.contactInfo.email,
        isEmailPublic: updates.contactInfo.isEmailPublic,
        website: updates.contactInfo.website,
        portfolioUrl: updates.contactInfo.portfolioUrl,
        businessAddress: updates.contactInfo.businessAddress ? {
          street: updates.contactInfo.businessAddress.street,
          city: updates.contactInfo.businessAddress.city,
          state: updates.contactInfo.businessAddress.state,
          postalCode: updates.contactInfo.businessAddress.postalCode,
          country: updates.contactInfo.businessAddress.country,
          isPublic: updates.contactInfo.businessAddress.isPublic
        } : undefined
      } : undefined
    };

    return this.http.put<ApiResponse<UserProfile>>(`${this.API_URL}`, updateRequest, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(response => response.data!),
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {
    const createRequest: CreateProfileRequest = {
      username: profileData.username!,
      firstName: profileData.firstName!,
      lastName: profileData.lastName!,
      professionalTitle: profileData.professionalTitle,
      headline: profileData.headline,
      isPublic: profileData.isPublic ?? true
    };

    return this.http.post<ApiResponse<UserProfile>>(`${this.API_URL}`, createRequest, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(response => response.data!),
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  deleteProfile(): Observable<void> {
    return this.http.delete<ApiResponse<boolean>>(`${this.API_URL}`, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(() => void 0),
        tap(() => this.currentProfileSubject.next(null)),
        catchError(this.handleError)
      );
  }

  // Profile Search
  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {
    const searchRequest: ProfileSearchRequest = {
      location: filters.location,
      skills: filters.skills || [],
      sortBy: filters.sortBy || 'relevance',
      page: page,
      pageSize: limit
    };

    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(response => response.data!),
        catchError(this.handleError)
      );
  }

  // Search profiles by term
  searchProfilesByTerm(searchTerm: string, page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {
    const searchRequest: ProfileSearchRequest = {
      searchTerm: searchTerm,
      skills: [],
      sortBy: 'relevance',
      page: page,
      pageSize: pageSize
    };

    return this.http.post<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/search`, searchRequest, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(response => response.data!),
        catchError(this.handleError)
      );
  }

  // Get public profiles
  getPublicProfiles(page: number = 1, pageSize: number = 20): Observable<ProfileSearchResult> {
    return this.http.get<ApiResponse<ProfileSearchResult>>(`${this.API_URL}/public?page=${page}&pageSize=${pageSize}`)
      .pipe(
        map(response => response.data!),
        catchError(this.handleError)
      );
  }

  // Skills Management (TODO: Implement in backend)
  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {
    // For now, return a mock skill until backend implements this
    const newSkill: ProfileSkill = {
      ...skill,
      id: Date.now(),
      endorsements: 0,
      isEndorsedByCurrentUser: false
    };
    return new Observable(observer => {
      observer.next(newSkill);
      observer.complete();
    });
  }

  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {
    // TODO: Implement in backend
    return new Observable(observer => {
      observer.next({ ...updates, id: skillId } as ProfileSkill);
      observer.complete();
    });
  }

  deleteSkill(skillId: number): Observable<void> {
    // TODO: Implement in backend
    return new Observable(observer => {
      observer.next();
      observer.complete();
    });
  }

  endorseSkill(request: SkillEndorsementRequest): Observable<void> {
    // TODO: Implement in backend
    return new Observable(observer => {
      observer.next();
      observer.complete();
    });
  }

  // Experience Management
  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {
    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)
      .pipe(catchError(this.handleError));
  }

  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {
    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)
      .pipe(catchError(this.handleError));
  }

  deleteExperience(experienceId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)
      .pipe(catchError(this.handleError));
  }

  // Portfolio Management
  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {
    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)
      .pipe(catchError(this.handleError));
  }

  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {
    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)
      .pipe(catchError(this.handleError));
  }

  deletePortfolioItem(itemId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)
      .pipe(catchError(this.handleError));
  }

  // Achievements & Certifications
  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {
    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)
      .pipe(catchError(this.handleError));
  }

  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {
    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)
      .pipe(catchError(this.handleError));
  }

  // Blog Posts
  getBlogPosts(profileId: number): Observable<BlogPost[]> {
    // Blog posts are included in the profile data from the backend
    // Return empty array for now, as they're part of the main profile
    return new Observable(observer => {
      observer.next([]);
      observer.complete();
    });
  }

  // Analytics
  getProfileAnalytics(): Observable<ProfileAnalytics> {
    // For now, return mock analytics since backend doesn't have this endpoint yet
    const mockAnalytics: ProfileAnalytics = {
      profileViews: 0,
      uniqueVisitors: 0,
      viewsThisMonth: 0,
      viewsThisWeek: 0,
      topReferrers: [],
      skillEndorsements: 0,
      blogPostViews: 0,
      contactButtonClicks: 0
    };
    return new Observable(observer => {
      observer.next(mockAnalytics);
      observer.complete();
    });
  }

  recordProfileView(request: ProfileViewRequest): Observable<void> {
    return this.http.post<ApiResponse<boolean>>(`${this.API_URL}/view`, request, {
      headers: this.getHttpHeaders()
    })
      .pipe(
        map(() => void 0),
        catchError(this.handleError)
      );
  }

  // File Upload
  uploadProfilePhoto(file: File): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('authToken');
    let headers = new HttpHeaders();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/profile-photo`, formData, {
      headers: headers
    })
      .pipe(
        map(response => ({ url: response.data! })),
        catchError(this.handleError)
      );
  }

  uploadCoverPhoto(file: File): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('authToken');
    let headers = new HttpHeaders();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return this.http.post<ApiResponse<string>>(`${this.API_URL}/upload/cover-photo`, formData, {
      headers: headers
    })
      .pipe(
        map(response => ({ url: response.data! })),
        catchError(this.handleError)
      );
  }

  // Utility Methods
  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {
    // Generate slug client-side for now
    const slug = `${firstName.toLowerCase()}-${lastName.toLowerCase()}`
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    return new Observable(observer => {
      observer.next({ slug });
      observer.complete();
    });
  }

  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {
    // For now, assume all slugs are available
    // TODO: Implement in backend
    return new Observable(observer => {
      observer.next({ available: true });
      observer.complete();
    });
  }

  // Social Sharing
  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {
    // Generate share data from current profile
    const currentProfile = this.currentProfileSubject.value;
    if (currentProfile) {
      const shareData = {
        title: `${currentProfile.firstName} ${currentProfile.lastName} - ${currentProfile.professionalTitle}`,
        description: currentProfile.summary || currentProfile.headline || 'Professional profile',
        imageUrl: currentProfile.profilePhotoUrl || '',
        url: `${window.location.origin}/profile/${currentProfile.slug}`
      };

      return new Observable(observer => {
        observer.next(shareData);
        observer.complete();
      });
    }

    return throwError(() => new Error('Profile not found'));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error('ProfileService error:', error);

    let errorMessage = 'Възникна неочаквана грешка';

    if (error.error && error.error.message) {
      // Backend returned an error message
      errorMessage = error.error.message;
    } else if (error.status === 0) {
      errorMessage = 'Няма връзка със сървъра';
    } else if (error.status === 401) {
      errorMessage = 'Не сте упълномощени за тази операция';
    } else if (error.status === 403) {
      errorMessage = 'Нямате права за тази операция';
    } else if (error.status === 404) {
      errorMessage = 'Профилът не е намерен';
    } else if (error.status >= 500) {
      errorMessage = 'Сървърна грешка. Моля, опитайте отново по-късно';
    }

    return throwError(() => new Error(errorMessage));
  }
}
